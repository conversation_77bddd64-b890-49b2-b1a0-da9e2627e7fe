<x-unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('dashboard') }}" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Create Post</h1>
                <p class="text-gray-600 mt-1">Share what's on your mind with the community</p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-3xl">
        <form action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            
            <!-- User Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <img class="h-12 w-12 rounded-full" src="{{ auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                    <div>
                        <p class="font-medium text-gray-900">{{ auth()->user()->name }}</p>
                        <select name="organization_id" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 p-0">
                            <option value="">Personal Post</option>
                            @foreach($organizations as $org)
                                <option value="{{ $org->id }}">{{ $org->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Post Method -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Post Method</label>
                    <select name="post_method_id" id="post_method_select" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required onchange="loadTags()">
                        <option value="">Select post method...</option>
                        @foreach($postMethods as $method)
                            <option value="{{ $method->id }}" {{ old('post_method_id') == $method->id ? 'selected' : '' }}>
                                {{ $method->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('post_method_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Tags -->
                <div class="mb-6" id="tags_section" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <div id="tags_container" class="space-y-2">
                        <!-- Tags will be loaded dynamically -->
                    </div>
                    @error('tags')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Post Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Post Type</label>
                    <select name="type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required>
                        <option value="general">General</option>
                        <option value="announcement">Announcement</option>
                        <option value="event">Event</option>
                        <option value="financial_report">Financial Report</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Title -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" name="title" value="{{ old('title') }}" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green text-lg" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Content -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                    <textarea name="content" rows="6" placeholder="What's on your mind?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required>{{ old('content') }}</textarea>
                    @error('content')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Image Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Images (Optional)</label>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center space-x-2 cursor-pointer text-custom-green hover:text-custom-second-darkest">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-sm font-medium">Add Photos</span>
                            <input type="file" name="images[]" multiple accept="image/*" class="hidden" onchange="previewImages(this)">
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">You can upload up to 5 images. Max 2MB each.</p>
                    
                    <!-- Image Preview -->
                    <div id="image-preview" class="mt-3 grid grid-cols-2 gap-2 hidden"></div>
                    
                    @error('images')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @error('images.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Facebook Embed URL -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Facebook Embed URL (Optional)</label>
                    <input type="url" name="facebook_embed_url" value="{{ old('facebook_embed_url') }}" placeholder="https://www.facebook.com/..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                    @error('facebook_embed_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Post Options -->
                <div class="mb-6">
                    <div class="flex items-center space-x-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_pinned" value="1" class="rounded border-gray-300 text-custom-green shadow-sm focus:border-custom-green focus:ring focus:ring-custom-green focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Pin this post</span>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <input type="hidden" name="status" value="published">
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('dashboard') }}" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Cancel
                </a>
                <button type="submit" name="status" value="draft" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Save as Draft
                </button>
                <button type="submit" name="status" value="published" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Publish Post
                </button>
            </div>
        </form>
    </div>

    <script>
    function previewImages(input) {
        const preview = document.getElementById('image-preview');
        preview.innerHTML = '';
        
        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');
            
            Array.from(input.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg">
                            <button type="button" onclick="removeImage(${index}, this)" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function removeImage(index, button) {
        const input = document.querySelector('input[name="images[]"]');
        const dt = new DataTransfer();

        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });

        input.files = dt.files;
        button.parentElement.remove();

        if (input.files.length === 0) {
            document.getElementById('image-preview').classList.add('hidden');
        }
    }

    // Load tags based on selected post method
    async function loadTags() {
        const postMethodSelect = document.getElementById('post_method_select');
        const tagsSection = document.getElementById('tags_section');
        const tagsContainer = document.getElementById('tags_container');

        const postMethodId = postMethodSelect.value;

        if (!postMethodId) {
            tagsSection.style.display = 'none';
            tagsContainer.innerHTML = '';
            return;
        }

        try {
            const response = await fetch(`{{ route('api.post-methods.tags') }}?post_method_id=${postMethodId}`);
            const data = await response.json();

            if (data.tags && data.tags.length > 0) {
                tagsContainer.innerHTML = '';

                // Create checkboxes for each tag
                data.tags.forEach(tag => {
                    const tagDiv = document.createElement('div');
                    tagDiv.className = 'flex items-center';
                    tagDiv.innerHTML = `
                        <input type="checkbox" name="tags[]" value="${tag.id}" id="tag_${tag.id}"
                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                        <label for="tag_${tag.id}" class="ml-2 text-sm text-gray-700 flex items-center">
                            <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: ${tag.color}"></span>
                            ${tag.name}
                        </label>
                    `;
                    tagsContainer.appendChild(tagDiv);
                });

                tagsSection.style.display = 'block';
            } else {
                tagsSection.style.display = 'none';
                tagsContainer.innerHTML = '';
            }
        } catch (error) {
            console.error('Error loading tags:', error);
            tagsSection.style.display = 'none';
            tagsContainer.innerHTML = '';
        }
    }

    // Load tags on page load if post method is already selected
    document.addEventListener('DOMContentLoaded', function() {
        const postMethodSelect = document.getElementById('post_method_select');
        if (postMethodSelect.value) {
            loadTags();
        }
    });
    </script>
</x-unilink-layout>
