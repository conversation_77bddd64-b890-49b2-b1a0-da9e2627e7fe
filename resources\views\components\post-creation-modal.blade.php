@props(['group' => null, 'organization' => null])

<!-- Post Creation Modal -->
<div id="postCreationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    @if($group)
                        Create Post in {{ $group->name }}
                    @elseif($organization)
                        Create Announcement for {{ $organization->name }}
                    @else
                        Create Post
                    @endif
                </h3>
                <button onclick="closePostModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf
            
            @if($group)
                <input type="hidden" name="group_id" value="{{ $group->id }}">
            @elseif($organization)
                <input type="hidden" name="organization_id" value="{{ $organization->id }}">
            @endif

            <!-- User Info -->
            <div class="flex items-center space-x-3">
                <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ? Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ auth()->user()->name }}">
                <div>
                    <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name }}</p>
                    <p class="text-xs text-gray-500">
                        @if($group)
                            Posting to {{ $group->name }}
                            @if($group->post_approval === 'required')
                                <span class="text-yellow-600">(Requires approval)</span>
                            @endif
                        @elseif($organization)
                            Official announcement for {{ $organization->name }}
                        @endif
                    </p>
                </div>
            </div>

            <!-- Post Method -->
            <div>
                <label for="post_method_id" class="block text-sm font-medium text-gray-700 mb-1">Post Method</label>
                <select name="post_method_id" id="post_method_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required onchange="loadModalTags()">
                    <option value="">Select post method...</option>
                    @php
                        $postMethods = \App\Models\PostMethod::with('activeTags')->active()->get();
                    @endphp
                    @foreach($postMethods as $method)
                        <option value="{{ $method->id }}">{{ $method->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Tags -->
            <div id="modal_tags_section" style="display: none;">
                <label class="block text-sm font-medium text-gray-700 mb-1">Tags</label>
                <div id="modal_tags_container" class="space-y-2 max-h-32 overflow-y-auto">
                    <!-- Tags will be loaded dynamically -->
                </div>
            </div>

            <!-- Post Type -->
            <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Post Type</label>
                <select name="type" id="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="general">General</option>
                    <option value="announcement">Announcement</option>
                    <option value="event">Event</option>
                    @if($organization)
                        <option value="financial_report">Financial Report</option>
                    @endif
                </select>
            </div>

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                <input type="text" name="title" id="title" required
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                       placeholder="What's the title of your post?">
            </div>

            <!-- Content -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content *</label>
                <textarea name="content" id="content" rows="4" required
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Share your thoughts, updates, or information..."></textarea>
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Images</label>
                <x-file-upload-zone
                    name="images"
                    accept="image/*"
                    :multiple="true"
                    max-size="10MB"
                    :allowed-types="['jpg', 'jpeg', 'png', 'gif']"
                    title="Upload Images"
                    description="Drag and drop images here or click to browse" />
            </div>

            <!-- File Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">File Attachments</label>
                @if($group && $group->allow_file_sharing)
                    <!-- Group post attachments with group-specific settings -->
                    <x-file-upload-zone
                        name="attachments"
                        accept="{{ $group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*' }}"
                        :multiple="true"
                        max-size="{{ $group->max_file_size_mb }}MB"
                        :allowed-types="$group->allowed_file_types"
                        title="Upload Files"
                        description="Drag and drop files here or click to browse" />
                @elseif($group && !$group->allow_file_sharing)
                    <!-- Group doesn't allow file sharing -->
                    <div class="text-sm text-gray-500 italic">
                        File attachments are not allowed in this group.
                    </div>
                @else
                    <!-- Normal post attachments with default settings -->
                    <x-file-upload-zone
                        name="attachments"
                        accept=".pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z"
                        :multiple="true"
                        max-size="10MB"
                        :allowed-types="['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z']"
                        title="Upload Files"
                        description="Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)" />
                @endif
            </div>

            <!-- Pin Post (for moderators/admins) -->
            @if(($group && $group->userCanModerate(auth()->user())) || ($organization && $organization->userCanPost(auth()->user())))
                <div class="flex items-center">
                    <input type="checkbox" name="is_pinned" id="is_pinned" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_pinned" class="ml-2 text-sm text-gray-900">
                        Pin this post to the top
                    </label>
                </div>
            @endif

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <button type="button" onclick="closePostModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                    Cancel
                </button>
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700">
                    @if($group && $group->post_approval === 'required')
                        Submit for Approval
                    @else
                        Post
                    @endif
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function openCreatePostModal() {
        document.getElementById('postCreationModal').classList.remove('hidden');
        document.getElementById('postCreationModal').classList.add('flex');
        document.body.style.overflow = 'hidden';
    }

    function closePostModal() {
        document.getElementById('postCreationModal').classList.add('hidden');
        document.getElementById('postCreationModal').classList.remove('flex');
        document.body.style.overflow = 'auto';

        // Reset form
        document.querySelector('#postCreationModal form').reset();

        // Reset file upload zones
        const imagePreviews = document.querySelectorAll('#postCreationModal [id$="-preview"]');
        imagePreviews.forEach(preview => {
            preview.classList.add('hidden');
            preview.innerHTML = '';
        });

        // Reset tags section
        document.getElementById('modal_tags_section').style.display = 'none';
        document.getElementById('modal_tags_container').innerHTML = '';
    }

    // Load tags for modal based on selected post method
    async function loadModalTags() {
        const postMethodSelect = document.getElementById('post_method_id');
        const tagsSection = document.getElementById('modal_tags_section');
        const tagsContainer = document.getElementById('modal_tags_container');

        const postMethodId = postMethodSelect.value;

        if (!postMethodId) {
            tagsSection.style.display = 'none';
            tagsContainer.innerHTML = '';
            return;
        }

        try {
            const response = await fetch(`{{ route('api.post-methods.tags') }}?post_method_id=${postMethodId}`);
            const data = await response.json();

            if (data.tags && data.tags.length > 0) {
                tagsContainer.innerHTML = '';

                // Create checkboxes for each tag
                data.tags.forEach(tag => {
                    const tagDiv = document.createElement('div');
                    tagDiv.className = 'flex items-center';
                    tagDiv.innerHTML = `
                        <input type="checkbox" name="tags[]" value="${tag.id}" id="modal_tag_${tag.id}"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="modal_tag_${tag.id}" class="ml-2 text-sm text-gray-700 flex items-center">
                            <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: ${tag.color}"></span>
                            ${tag.name}
                        </label>
                    `;
                    tagsContainer.appendChild(tagDiv);
                });

                tagsSection.style.display = 'block';
            } else {
                tagsSection.style.display = 'none';
                tagsContainer.innerHTML = '';
            }
        } catch (error) {
            console.error('Error loading tags:', error);
            tagsSection.style.display = 'none';
            tagsContainer.innerHTML = '';
        }
    }
</script>
