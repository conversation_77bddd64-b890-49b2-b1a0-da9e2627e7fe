<?php

namespace Tests\Feature;

use App\Models\Post;
use App\Models\PostMethod;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PostTaggingSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed the post methods and tags
        $this->artisan('db:seed', ['--class' => 'PostMethodSeeder']);
    }

    /** @test */
    public function it_can_create_post_methods_and_tags()
    {
        $this->assertDatabaseHas('post_methods', ['slug' => 'user']);
        $this->assertDatabaseHas('post_methods', ['slug' => 'organization']);
        $this->assertDatabaseHas('post_methods', ['slug' => 'group']);

        $userMethod = PostMethod::where('slug', 'user')->first();
        $this->assertTrue($userMethod->tags()->count() > 0);
        
        $this->assertDatabaseHas('tags', ['name' => 'Personal']);
        $this->assertDatabaseHas('tags', ['name' => 'Announcement']);
        $this->assertDatabaseHas('tags', ['name' => 'Discussion']);
    }

    /** @test */
    public function it_can_create_post_with_tags()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $personalTag = Tag::where('name', 'Personal')->first();
        $reflectionTag = Tag::where('name', 'Reflection')->first();

        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Test Post with Tags',
            'content' => 'This is a test post with tags.',
            'type' => 'general',
            'post_method_id' => $userMethod->id,
            'tags' => [$personalTag->id, $reflectionTag->id],
            'status' => 'published'
        ]);

        $response->assertRedirect();
        
        $post = Post::where('title', 'Test Post with Tags')->first();
        $this->assertNotNull($post);
        $this->assertEquals($userMethod->id, $post->post_method_id);
        $this->assertEquals(2, $post->tags()->count());
        $this->assertTrue($post->tags->contains($personalTag));
        $this->assertTrue($post->tags->contains($reflectionTag));
    }

    /** @test */
    public function it_can_load_tags_by_post_method()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();

        $response = $this->actingAs($user)->get("/api/post-methods/tags?post_method_id={$userMethod->id}");

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertArrayHasKey('tags', $data);
        $this->assertGreaterThan(0, count($data['tags']));
        
        // Check that all returned tags belong to the user method
        foreach ($data['tags'] as $tag) {
            $this->assertDatabaseHas('tags', [
                'id' => $tag['id'],
                'post_method_id' => $userMethod->id
            ]);
        }
    }

    /** @test */
    public function it_can_filter_posts_by_tags()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $personalTag = Tag::where('name', 'Personal')->first();
        $reflectionTag = Tag::where('name', 'Reflection')->first();

        // Create posts with different tags
        $post1 = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'title' => 'Personal Post',
            'status' => 'published',
            'published_at' => now()
        ]);
        $post1->tags()->attach($personalTag->id);

        $post2 = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'title' => 'Reflection Post',
            'status' => 'published',
            'published_at' => now()
        ]);
        $post2->tags()->attach($reflectionTag->id);

        $post3 = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'title' => 'Mixed Post',
            'status' => 'published',
            'published_at' => now()
        ]);
        $post3->tags()->attach([$personalTag->id, $reflectionTag->id]);

        // Test filtering by personal tag
        $response = $this->actingAs($user)->get("/posts-filter?tags[]={$personalTag->id}");
        $response->assertStatus(200);
        
        // Should return post1 and post3 (both have personal tag)
        $data = $response->json();
        $this->assertEquals(2, $data['count']);

        // Test filtering by reflection tag
        $response = $this->actingAs($user)->get("/posts-filter?tags[]={$reflectionTag->id}");
        $response->assertStatus(200);
        
        // Should return post2 and post3 (both have reflection tag)
        $data = $response->json();
        $this->assertEquals(2, $data['count']);
    }

    /** @test */
    public function it_can_filter_posts_by_post_method()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $orgMethod = PostMethod::where('slug', 'organization')->first();

        // Create posts with different methods
        Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $orgMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Test filtering by user method
        $response = $this->actingAs($user)->get("/posts-filter?post_method_id={$userMethod->id}");
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(1, $data['count']);
    }

    /** @test */
    public function it_displays_tags_in_post_cards()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $personalTag = Tag::where('name', 'Personal')->first();

        $post = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'title' => 'Tagged Post',
            'status' => 'published',
            'published_at' => now()
        ]);
        $post->tags()->attach($personalTag->id);

        $response = $this->actingAs($user)->get('/dashboard');
        $response->assertStatus(200);
        $response->assertSee($personalTag->name);
        $response->assertSee($userMethod->name);
    }
}
